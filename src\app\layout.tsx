import { ErrorBoundary } from '@/components/error-boundary';
import { TipsProvider } from '@/components/ui/Tips';
import { TooltipProvider } from '@/components/ui/Tooltip';
import { DeviceProvider } from '@/context/DeviceContext';
import { AuthProvider } from '@/components/auth/AuthProvider';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { getDeviceType } from '@/lib/device';
import type { Metadata } from 'next';
import type { Viewport } from 'next';
import { Geologica } from 'next/font/google';
import { headers } from 'next/headers';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getTranslations } from 'next-intl/server';
import './globals.css';

// 配置 Geologica 字体
const geologica = Geologica({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-geologica',
});

// 配置等宽字体变量（用于代码显示，使用系统默认等宽字体）
const geistMono = {
  variable: '--font-geist-mono',
};

/**
 * 动态生成元数据，用于 SEO 和浏览器标签显示。
 */
export async function generateMetadata(): Promise<Metadata> {
  const metadata = await getTranslations('metadata');

  return {
    title: metadata('title'),
    description: metadata('description'),
    keywords: [
      'AI',
      'background removal',
      'image editing',
      'remove.bg',
      'fotor',
      'background replacement',
    ],
    authors: [{ name: 'AI Background Remover' }],
  };
}

/**
 * 应用的视口配置，用于控制页面在不同设备上的显示。
 * 移动端使用宽度优先，桌面端使用高度优先。
 */
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1.0,
  minimumScale: 1.0,
  viewportFit: 'cover',
  userScalable: false,
};

/**
 * 根布局组件，包裹整个应用。
 * 集成了设备检测功能，为移动端和桌面端提供不同的用户体验。
 * 使用 Geologica 作为主要字体，提供现代化的中文和英文显示效果。
 * @param {object} props - 组件 props
 * @param {React.ReactNode} props.children - 子组件
 * @returns {JSX.Element}
 */
export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // 获取请求头信息，用于服务端设备检测
  const headersList = await headers();
  const userAgent = headersList.get('user-agent') || '';

  // 服务端设备检测
  const initialDeviceType = getDeviceType(userAgent);

  // 获取当前语言
  const locale = await getLocale();

  // 导入语言配置以获取文本方向
  const { localeConfig } = await import('@/i18n/config');
  const direction =
    localeConfig[locale as keyof typeof localeConfig]?.dir || 'ltr';

  return (
    <html lang={locale} dir={direction} style={{ touchAction: 'none' }}>
      <body
        className={`${geologica.variable} ${geistMono.variable} antialiased`}
      >
        <NextIntlClientProvider>
          <TipsProvider>
            <TooltipProvider>
              <DeviceProvider initialDeviceType={initialDeviceType}>
                <ErrorBoundary>
                  <AuthProvider>
                    <AuthGuard>{children}</AuthGuard>
                  </AuthProvider>
                </ErrorBoundary>
              </DeviceProvider>
            </TooltipProvider>
          </TipsProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
