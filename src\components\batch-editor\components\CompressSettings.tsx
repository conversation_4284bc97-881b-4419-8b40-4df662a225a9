'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/base';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/Tabs';
import Image from 'next/image';
import { useTips } from '@/components/ui/Tips';
import { shouldDisableBatchApplyButton } from '../../../lib/buttonUtils';
import type { CompressionLevel } from '@/lib/imageUtils/imageCompress';
import { useTranslations } from 'next-intl';

interface CompressSettings {
  level?: CompressionLevel;
  customSize?: number;
  customUnit?: 'KB' | 'MB';
}

interface CompressSettingsProps {
  onApply?: (settings: CompressSettings) => void;
  images?: Array<{
    id: string;
    status: string;
    originalSize?: number;
    compressedSize?: number;
    size: number;
  }>;
  isProcessing?: boolean;
}

export function CompressSettings({
  onApply,
  images = [],
  isProcessing = false,
}: CompressSettingsProps) {
  const batchEditor = useTranslations('batchEditor');
  const common = useTranslations('common');
  const messages = useTranslations('messages');

  // 压缩程度选项（更新描述以反映新的压缩效果）
  const COMPRESSION_LEVELS = [
    {
      id: 'original',
      name: batchEditor('compress.original'),
    },
    {
      id: 'light',
      name: batchEditor('compress.lightBestClarity'),
    },
    {
      id: 'medium',
      name: batchEditor('compress.mediumBalanced'),
    },
    {
      id: 'deep',
      name: batchEditor('compress.highSmallest'),
    },
  ] as const;

  const { showTips } = useTips();
  const [compressionMode, setCompressionMode] = useState<'level' | 'custom'>(
    'level'
  );
  const [selectedLevel, setSelectedLevel] =
    useState<CompressionLevel>('medium');
  const [customSize, setCustomSize] = useState<string>('');
  const [customUnit, setCustomUnit] = useState<'KB' | 'MB'>('KB');
  const [validationError, setValidationError] = useState<string>('');

  // Validation function for custom size input
  const validateCustomSize = (value: string, unit: 'KB' | 'MB') => {
    if (!value) {
      setValidationError('');
      return;
    }

    const sizeNum = parseFloat(value);
    if (isNaN(sizeNum) || sizeNum <= 0) {
      setValidationError('');
      return;
    }

    const sizeInKB = unit === 'MB' ? sizeNum * 1024 : sizeNum;

    if (sizeInKB < 20) {
      setValidationError('invalid');
    } else if (sizeInKB > 10240) {
      setValidationError('invalid');
    } else {
      setValidationError('');
    }
  };

  // Calculate min and max values based on unit
  const getInputLimits = () => {
    if (customUnit === 'KB') {
      return { min: 20, max: 10240, step: 1 };
    } else {
      return { min: 0.02, max: 10, step: 0.001 };
    }
  };

  const handleApply = () => {
    if (!onApply) return;

    if (compressionMode === 'level') {
      // 按压缩程度
      onApply({
        level: selectedLevel,
      });
    } else {
      // 自定义体积
      const sizeNum = parseFloat(customSize);

      if (isNaN(sizeNum) || sizeNum <= 0) {
        showTips('error', messages('batchEditor.pleaseEnterValidFileSize'));
        return;
      }

      // 验证大小范围
      const sizeInKB = customUnit === 'MB' ? sizeNum * 1024 : sizeNum;
      if (sizeInKB < 20) {
        showTips('error', messages('batchEditor.fileSizeMinimum'));
        return;
      }
      if (sizeInKB > 10240) {
        // 10MB
        showTips('error', messages('batchEditor.fileSizeMaximum'));
        return;
      }

      onApply({
        customSize: sizeNum,
        customUnit,
      });
    }
  };

  return (
    <div className='w-[344px] bg-white border-r border-[#E7E7E7] flex flex-col h-full'>
      {/* 头部标题区域 */}
      <div className='pt-6 px-4 mb-4'>
        <div className='border-b border-border pb-4'>
          <h2 className='text-base font-bold'>
            {batchEditor('interface.compress')}
          </h2>
        </div>
      </div>

      {/* 压缩模式选择和内容区域 */}
      <div className='flex-1 flex flex-col'>
        <Tabs
          value={compressionMode}
          onValueChange={value =>
            setCompressionMode(value as 'level' | 'custom')
          }
        >
          {/* Tab 选择器 */}
          <div className='px-4'>
            <TabsList className='w-full h-auto bg-border p-0.5'>
              <TabsTrigger
                value='level'
                className='data-[state=inactive]:text-[#878787] cursor-pointer'
              >
                {batchEditor('compress.compressionLevel')}
              </TabsTrigger>
              <TabsTrigger
                value='custom'
                className='data-[state=inactive]:text-[#878787] cursor-pointer'
              >
                {batchEditor('compress.customSize')}
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Tab 内容区域 */}
          <div className='flex-1 px-4'>
            <TabsContent value='level' className='space-y-2 mt-2 h-full'>
              {/* 按压缩程度 */}
              {COMPRESSION_LEVELS.map(level => (
                <button
                  key={level.id}
                  onClick={() => setSelectedLevel(level.id as CompressionLevel)}
                  className={cn(
                    'w-full p-3 bg-white rounded-lg border text-left transition-colors cursor-pointer',
                    selectedLevel === level.id
                      ? 'border-[#FFCC03] bg-[#FFFBF0]'
                      : 'border-[#E7E7E7] hover:bg-gray-50'
                  )}
                >
                  <div className='flex items-center justify-between'>
                    <div className='flex-1'>
                      <div className='text-sm font-medium text-[#121212]'>
                        {level.name}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </TabsContent>

            <TabsContent value='custom' className='space-y-4 mt-2 h-full'>
              {/* 自定义体积 */}
              <div className='text-sm text-[#121212] font-medium'>
                {batchEditor('compress.eachImageUnder')}
              </div>

              <div className='flex items-center gap-2'>
                <div className='flex-1'>
                  <Input
                    type='number'
                    value={customSize}
                    onChange={e => {
                      const value = e.target.value;
                      if (value === '' || /^\d*\.?\d*$/.test(value)) {
                        setCustomSize(value);
                        validateCustomSize(value, customUnit);
                      }
                    }}
                    placeholder={batchEditor('compress.enterNumberOnly')}
                    className={`h-10 text-sm border-[#E7E7E7] rounded-lg ${
                      validationError ? 'border-red-500' : ''
                    }`}
                    min={getInputLimits().min}
                    max={getInputLimits().max}
                    step={getInputLimits().step}
                  />
                </div>

                <Select
                  value={customUnit}
                  onValueChange={(value: 'KB' | 'MB') => {
                    setCustomUnit(value);
                    if (customSize) {
                      validateCustomSize(customSize, value);
                    }
                  }}
                >
                  <SelectTrigger className='w-20 h-10 bg-white border border-[#E7E7E7] rounded-lg px-3'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className='bg-white border border-[#E7E7E7] rounded-xl shadow-lg py-2 px-2'>
                    <SelectItem value='KB' className='h-8 text-sm'>
                      KB
                    </SelectItem>
                    <SelectItem value='MB' className='h-8 text-sm'>
                      MB
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='text-xs text-text-secondary'>
                {batchEditor('compress.supportedRange')}:{' '}
                {customUnit === 'KB' ? '20 KB – 10240 KB' : '0.02 MB – 10 MB'}
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* 底部应用按钮 */}
      <div className='px-4 py-6 bg-white rounded-b-2xl'>
        <Button
          onClick={handleApply}
          disabled={shouldDisableBatchApplyButton(isProcessing, images)}
          className='w-full h-12 bg-[#FFCC03] hover:bg-[#FFCC03]/90 text-[#121212] text-base font-medium rounded-xl disabled:opacity-50'
          size='lg'
        >
          {isProcessing ? (
            <div className='flex items-center gap-2'>
              <Image
                src='/apps/icons/loading.png'
                alt='loading'
                width={16}
                height={16}
                className='animate-spin'
              />
              {common('processing')}
            </div>
          ) : (
            common('apply')
          )}
        </Button>
      </div>
    </div>
  );
}
