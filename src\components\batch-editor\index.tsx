'use client';

import React, { useState } from 'react';
import { SideNavigation } from './components/SideNavigation';
import { BackgroundSettings } from './components/BackgroundSettings';
import { ResizeSettings } from './components/ResizeSettings';
import { RenameSettings } from './components/RenameSettings';
import { ConvertSettings } from './components/ConvertSettings';
import { CompressSettings } from './components/CompressSettings';
import { ImageUploadGrid } from './components/ImageUploadArea';
import { Header } from '@/components/common/Header';
import { useBatchImageUpload } from '../../hooks/batch-editor/useBatchImageUpload';
import { useBatchImageStorage } from '../../hooks/batch-editor/useBatchImageStorage';
import { useImageEditor } from '../../hooks/background-remover/useImageEditor';

import { useImageStore, type ImageState } from '@/store/imageStore';
import type { UploadedImage } from './components/BatchBackgroundImagePicker';

interface BackgroundSettings {
  type: 'color' | 'photos';
  color?: string;
  backgroundImageUrl?: string;
  backgroundImageId?: string;
}

interface BatchEditorProps {
  className?: string;
  isVipUser?: boolean; // 是否为会员用户
}

interface BackgroundSettings {
  type: 'color' | 'photos';
  color?: string;
  backgroundImageUrl?: string;
}

interface ResizeSettings {
  width?: number;
  height?: number;
  aspectRatio?: number; // 宽高比 (width/height)
  scale?: number; // 缩放比例 (0.2-2.0)
  mode: 'fixed' | 'ratio' | 'scale' | 'custom' | 'original'; // 固定尺寸、宽高比、缩放、自定义模式或原尺寸
  customMode?: 'scale' | 'dimensions'; // 自定义模式的子类型
  unit?: 'px' | 'in' | 'mm'; // 单位
  lockAspectRatio?: boolean; // 是否锁定宽高比
  resizeMode?: 'fit' | 'fill' | 'stretch'; // 缩放模式：适应、填充、拉伸
}

interface RenameSettings {
  prefix: string;
  startNumber: number;
  numberStep: number;
}

interface ConvertSettings {
  format: string;
}

interface CompressSettings {
  level?: 'original' | 'light' | 'medium' | 'deep';
  customSize?: number;
  customUnit?: 'KB' | 'MB';
}

/**
 * 批量编辑组件
 * 提供批量处理图片的功能，包括背景移除、尺寸调整、重命名、格式转换、压缩等
 */
export function BatchEditor({
  className,
  isVipUser = false,
}: BatchEditorProps) {
  // TODO: 当需要国际化硬编码文案时，再添加翻译hooks
  // const batchEditor = useTranslations('batchEditor');
  // const common = useTranslations('common');
  // const messages = useTranslations('messages');

  const [activeFeature, setActiveFeature] = useState('background');
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);

  // 注意：我们不再使用 performBatchOperation，让 temporal 自然地记录历史

  // 批量存储相关 hook
  const {
    initializationStatus,
    initializationError,
    uploadedBackgroundImages,
    addBackgroundImage,
  } = useBatchImageStorage();

  // 批量上传相关 hook
  const {
    isDragActive,
    getInputProps,
    getRootProps,
    open,
    handleLoadFromUrl,
    processingImageIds,
    batchRemoveBackground,
    addProcessingImages,
    removeProcessingImages,
    isLoadingApi,
  } = useBatchImageUpload(isVipUser);

  // 编辑相关 hook - 只取批量编辑需要的部分
  const { images } = useImageEditor(processingImageIds);

  const handleFeatureChange = (featureId: string) => {
    setActiveFeature(featureId);
  };

  // 处理背景图片上传
  const handleFileUpload = (file: File) => {
    const objectURL = URL.createObjectURL(file);
    const newImage: UploadedImage = {
      id: `uploaded-${Date.now()}`,
      url: objectURL,
      name: file.name,
      timestamp: Date.now(),
      file,
    };
    setUploadedImages(prev => [newImage, ...prev]);
  };

  // 包装批量背景去除操作
  const handleBatchRemoveBackground = async (imageIds: string[]) => {
    if (imageIds.length === 0) return;

    try {
      // 直接执行批量背景去除，让 temporal 自然地记录历史
      await batchRemoveBackground(imageIds);
    } catch (error) {
      console.error('批量背景去除失败:', error);
      throw error;
    }
  };

  const handleApplySettings = async (settings: BackgroundSettings) => {
    // 获取所有图片ID
    const imageIds = images.map(img => img.id);

    const imageStore = useImageStore.getState();

    try {
      // 准备批量更新数据
      const batchUpdates = images
        .filter(image => imageStore.images.get(image.id)) // 确保图片存在
        .map(image => ({
          imageId: image.id,
          updates: {
            backgroundColor: settings.color || 'transparent',
            backgroundImageUrl: settings.backgroundImageUrl,
            backgroundImageId: settings.backgroundImageId,
          },
        }));

      // 使用批量更新方法，这样只会创建一个历史记录项
      if (batchUpdates.length > 0) {
        imageStore.performBatchUpdate(batchUpdates);
      }

      // 异步更新所有图片的预览URL
      await useImageStore.getState().batchUpdatePreviewUrls(imageIds);
    } catch (error) {
      console.error('批量应用背景设置失败:', error);
      throw error;
    }
  };

  const handleApplyRename = async (settings: RenameSettings) => {
    // 获取所有非锁定图片的ID列表
    const imageIds = images
      .filter(img => img.status !== 'locked')
      .map(img => img.id);

    if (imageIds.length === 0) {
      return;
    }

    const lockedCount = images.length - imageIds.length;
    if (lockedCount > 0) {
    }

    try {
      // 添加所有要处理的图片到处理状态
      addProcessingImages(imageIds);

      // 执行批量重命名（已经是批量操作，只会创建一个历史记录项）
      await useImageStore
        .getState()
        .batchRenameImages(
          imageIds,
          settings.prefix,
          settings.startNumber,
          settings.numberStep,
          () => {
            // 进度回调
          }
        );
    } catch (error) {
      console.error('批量重命名失败:', error);
    } finally {
      // 无论成功还是失败，都要清理处理状态
      removeProcessingImages(imageIds);
    }
  };

  const handleApplyConvert = async (settings: ConvertSettings) => {
    // 获取所有非锁定图片的ID列表
    const imageIds = images
      .filter(img => img.status !== 'locked')
      .map(img => img.id);

    if (imageIds.length === 0) {
      return;
    }

    const lockedCount = images.length - imageIds.length;
    if (lockedCount > 0) {
    }

    // TODO: 使用简化的历史记录系统
    // const recordAfterOperation = recordBatchOperation(
    //   imageIds,
    //   'batch_convert',
    //   `批量格式转换: ${settings.format}`,
    //   { settings }
    // );

    try {
      // 添加所有要处理的图片到处理状态
      addProcessingImages(imageIds);

      // 执行批量格式转换
      await useImageStore
        .getState()
        .batchConvertImages(imageIds, settings.format, () => {
          // 进度回调
        });
    } catch (error) {
      console.error('批量格式转换失败:', error);
    } finally {
      // 无论成功还是失败，都要清理处理状态
      removeProcessingImages(imageIds);
    }
  };

  const handleApplyCompress = async (settings: CompressSettings) => {
    // 获取所有非锁定图片的ID列表
    const imageIds = images
      .filter(img => img.status !== 'locked')
      .map(img => img.id);

    if (imageIds.length === 0) {
      return;
    }

    const lockedCount = images.length - imageIds.length;
    if (lockedCount > 0) {
    }

    // TODO: 使用简化的历史记录系统
    // const recordAfterOperation = recordBatchOperation(
    //   imageIds,
    //   'batch_compress',
    //   settings.level
    //     ? `批量压缩: ${settings.level}`
    //     : `批量压缩: ${settings.customSize}${settings.customUnit}`,
    //   { settings }
    // );

    try {
      // 添加所有要处理的图片到处理状态
      addProcessingImages(imageIds);

      // 执行批量压缩
      await useImageStore
        .getState()
        .batchCompressImages(
          imageIds,
          settings.level,
          settings.customSize,
          settings.customUnit,
          () => {
            // 进度回调
          }
        );
    } catch (error) {
      console.error('批量压缩失败:', error);
    } finally {
      // 无论成功还是失败，都要清理处理状态
      removeProcessingImages(imageIds);
    }
  };

  // 恢复原始尺寸的辅助函数
  const handleRestoreOriginalSize = async (imageIds: string[]) => {
    const batchUpdates = imageIds
      .map(imageId => {
        const image = useImageStore.getState().images.get(imageId);
        if (!image || !image.originalWidth || !image.originalHeight) {
          return null;
        }

        return {
          imageId,
          updates: {
            width: image.originalWidth,
            height: image.originalHeight,
            targetWidth: undefined,
            targetHeight: undefined,
            resizedUrl: null,
            resizeMode: 'fit' as const,
          },
        };
      })
      .filter(update => update !== null) as Array<{
      imageId: string;
      updates: Partial<ImageState>;
    }>;

    if (batchUpdates.length > 0) {
      useImageStore.getState().performBatchUpdate(batchUpdates);
    }
  };

  // 固定尺寸调整的辅助函数
  const handleResizeToFixedSize = async (
    imageIds: string[],
    targetWidth: number,
    targetHeight: number
  ) => {
    // 固定使用智能裁剪模式
    const resizeMode = 'fill';
    // 并发处理所有图片的尺寸调整
    const promises = imageIds.map(async imageId => {
      const image = useImageStore.getState().images.get(imageId);
      if (!image || image.status === 'locked') {
        return { imageId, success: false, error: 'Skipped' };
      }

      try {
        // 确定要调整的图片URL
        const sourceUrl =
          image.processedUrl || image.resizedUrl || image.previewUrl;

        // 动态导入尺寸调整函数
        const { resizeImage } = await import('@/lib/imageUtils/imageResize');

        // 执行尺寸调整
        const resizeResult = await resizeImage(
          sourceUrl,
          targetWidth,
          targetHeight,
          resizeMode, // 使用传入的缩放模式
          0.9,
          'png'
        );

        return {
          imageId,
          success: true,
          resizedUrl: resizeResult.dataUrl,
          resizedSize: resizeResult.size,
        };
      } catch (error) {
        console.error(`图片 ${imageId} 尺寸调整失败:`, error);
        return { imageId, success: false, error: String(error) };
      }
    });

    const processResults = await Promise.all(promises);

    // 准备批量更新数据
    const batchUpdates = processResults
      .filter(result => result.success && result.resizedUrl)
      .map(result => {
        const image = useImageStore.getState().images.get(result.imageId);
        const updates: Partial<ImageState> = {
          width: targetWidth,
          height: targetHeight,
          targetWidth,
          targetHeight,
          resizeMode,
          resizedUrl: result.resizedUrl!,
          size: result.resizedSize!, // 更新当前文件大小
        };

        // 如果是第一次调整尺寸，保存原始尺寸
        if (image && (!image.originalWidth || !image.originalHeight)) {
          updates.originalWidth = image.width;
          updates.originalHeight = image.height;
        }

        return {
          imageId: result.imageId,
          updates,
        };
      });

    // 使用批量更新方法
    if (batchUpdates.length > 0) {
      useImageStore.getState().performBatchUpdate(batchUpdates);
    }
  };

  // 按宽高比调整的辅助函数
  const handleResizeByRatio = async (
    imageIds: string[],
    aspectRatio: number
  ) => {
    // 固定使用智能裁剪模式
    const resizeMode = 'fill';
    // 并发处理所有图片的尺寸调整
    const promises = imageIds.map(async imageId => {
      const image = useImageStore.getState().images.get(imageId);
      if (!image || image.status === 'locked') {
        return { imageId, success: false, error: 'Skipped' };
      }

      try {
        // 计算基于宽高比的目标尺寸
        const currentWidth = image.width;
        const currentHeight = image.height;

        let targetWidth: number;
        let targetHeight: number;

        // 根据当前尺寸和目标宽高比计算新尺寸
        if (currentWidth / currentHeight > aspectRatio) {
          // 当前图片比目标宽高比更宽，以高度为基准
          targetHeight = currentHeight;
          targetWidth = Math.round(targetHeight * aspectRatio);
        } else {
          // 当前图片比目标宽高比更窄，以宽度为基准
          targetWidth = currentWidth;
          targetHeight = Math.round(targetWidth / aspectRatio);
        }

        // 确定要调整的图片URL
        const sourceUrl =
          image.processedUrl || image.resizedUrl || image.previewUrl;

        // 动态导入尺寸调整函数
        const { resizeImage } = await import('@/lib/imageUtils/imageResize');

        // 执行尺寸调整
        const resizeResult = await resizeImage(
          sourceUrl,
          targetWidth,
          targetHeight,
          resizeMode,
          0.9,
          'png'
        );

        return {
          imageId,
          success: true,
          resizedUrl: resizeResult.dataUrl,
          resizedSize: resizeResult.size,
          targetWidth,
          targetHeight,
        };
      } catch (error) {
        console.error(`图片 ${imageId} 宽高比调整失败:`, error);
        return { imageId, success: false, error: String(error) };
      }
    });

    const processResults = await Promise.all(promises);

    // 准备批量更新数据
    const batchUpdates = processResults
      .filter(result => result.success && result.resizedUrl)
      .map(result => {
        const image = useImageStore.getState().images.get(result.imageId);
        const updates: Partial<ImageState> = {
          width: result.targetWidth!,
          height: result.targetHeight!,
          targetWidth: result.targetWidth!,
          targetHeight: result.targetHeight!,
          resizeMode,
          resizedUrl: result.resizedUrl!,
          size: result.resizedSize!, // 更新当前文件大小
        };

        // 如果是第一次调整尺寸，保存原始尺寸
        if (image && (!image.originalWidth || !image.originalHeight)) {
          updates.originalWidth = image.width;
          updates.originalHeight = image.height;
        }

        return {
          imageId: result.imageId,
          updates,
        };
      });

    // 使用批量更新方法
    if (batchUpdates.length > 0) {
      useImageStore.getState().performBatchUpdate(batchUpdates);
    }
  };

  // 按比例缩放的辅助函数
  const handleResizeByScale = async (imageIds: string[], scale: number) => {
    // 并发处理所有图片的尺寸调整
    const promises = imageIds.map(async imageId => {
      const image = useImageStore.getState().images.get(imageId);
      if (!image || image.status === 'locked') {
        return { imageId, success: false, error: 'Skipped' };
      }

      try {
        // 计算缩放后的尺寸
        const originalWidth = image.originalWidth || image.width;
        const originalHeight = image.originalHeight || image.height;
        const targetWidth = Math.round(originalWidth * scale);
        const targetHeight = Math.round(originalHeight * scale);

        // 确定要调整的图片URL
        const sourceUrl =
          image.processedUrl || image.resizedUrl || image.previewUrl;

        // 动态导入尺寸调整函数
        const { resizeImage } = await import('@/lib/imageUtils/imageResize');

        // 执行尺寸调整
        const resizeResult = await resizeImage(
          sourceUrl,
          targetWidth,
          targetHeight,
          'fit',
          0.9,
          'png'
        );

        return {
          imageId,
          success: true,
          resizedUrl: resizeResult.dataUrl,
          resizedSize: resizeResult.size,
          targetWidth,
          targetHeight,
        };
      } catch (error) {
        console.error(`图片 ${imageId} 比例缩放失败:`, error);
        return { imageId, success: false, error: String(error) };
      }
    });

    const processResults = await Promise.all(promises);

    // 准备批量更新数据
    const batchUpdates = processResults
      .filter(result => result.success && result.resizedUrl)
      .map(result => {
        const image = useImageStore.getState().images.get(result.imageId);
        const updates: Partial<ImageState> = {
          width: result.targetWidth!,
          height: result.targetHeight!,
          targetWidth: result.targetWidth!,
          targetHeight: result.targetHeight!,
          resizeMode: 'fit' as const,
          resizedUrl: result.resizedUrl!,
          size: result.resizedSize!, // 更新当前文件大小
        };

        // 如果是第一次调整尺寸，保存原始尺寸
        if (image && (!image.originalWidth || !image.originalHeight)) {
          updates.originalWidth = image.width;
          updates.originalHeight = image.height;
        }

        return {
          imageId: result.imageId,
          updates,
        };
      });

    // 使用批量更新方法
    if (batchUpdates.length > 0) {
      useImageStore.getState().performBatchUpdate(batchUpdates);
    }
  };

  const handleApplyResize = async (settings: ResizeSettings) => {
    const imageIds = images.map(img => img.id);
    if (imageIds.length === 0) {
      return;
    }

    try {
      // 添加所有要处理的图片到处理状态
      addProcessingImages(imageIds);

      switch (settings.mode) {
        case 'original':
          // 恢复原始尺寸
          await handleRestoreOriginalSize(imageIds);
          break;

        case 'fixed':
        case 'custom':
          // 固定尺寸或自定义尺寸
          if (settings.width && settings.height) {
            await handleResizeToFixedSize(
              imageIds,
              settings.width,
              settings.height
            );
          }
          break;

        case 'ratio':
          // 按宽高比调整
          if (settings.aspectRatio) {
            await handleResizeByRatio(imageIds, settings.aspectRatio);
          }
          break;

        case 'scale':
          // 按比例缩放
          if (settings.scale) {
            await handleResizeByScale(imageIds, settings.scale);
          }
          break;

        default:
      }
    } catch (error) {
      console.error('批量尺寸调整失败:', error);
    } finally {
      // 无论成功还是失败，都要清理处理状态
      removeProcessingImages(imageIds);
    }
  };

  const renderSettingsPanel = () => {
    switch (activeFeature) {
      case 'background':
        return (
          <BackgroundSettings
            onApply={handleApplySettings}
            onBatchRemoveBackground={handleBatchRemoveBackground}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
              processedUrl: img.processedUrl,
            }))}
            isProcessing={isLoadingApi}
            uploadedImages={uploadedImages}
            onFileUpload={handleFileUpload}
            uploadedBackgroundImages={uploadedBackgroundImages}
            onAddBackgroundImage={addBackgroundImage}
          />
        );
      case 'resize':
        return (
          <ResizeSettings
            onApply={handleApplyResize}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
              processedUrl: img.processedUrl,
              targetWidth: img.targetWidth,
              targetHeight: img.targetHeight,
              width: img.width,
              height: img.height,
            }))}
            isProcessing={isLoadingApi || processingImageIds.size > 0}
          />
        );
      case 'rename':
        return (
          <RenameSettings
            onApply={handleApplyRename}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
            }))}
            isProcessing={isLoadingApi || processingImageIds.size > 0}
          />
        );
      case 'convert':
        return (
          <ConvertSettings
            onApply={handleApplyConvert}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
              originalFormat: img.originalFormat,
            }))}
            isProcessing={isLoadingApi || processingImageIds.size > 0}
          />
        );
      case 'compress':
        return (
          <CompressSettings
            onApply={handleApplyCompress}
            images={images.map(img => ({
              id: img.id,
              status: img.status,
              originalSize: img.originalSize,
              compressedSize: img.compressedSize,
              size: img.size,
            }))}
            isProcessing={isLoadingApi || processingImageIds.size > 0}
          />
        );
      default:
        return null;
    }
  };

  // 如果存储初始化失败，显示错误信息
  if (initializationStatus === 'error') {
    return (
      <div className={`flex flex-col h-screen bg-white ${className || ''}`}>
        <Header variant='batch-editor' />
        <div className='flex-1 flex items-center justify-center'>
          <div className='text-center'>
            <h2 className='text-xl font-semibold text-red-600 mb-2'>
              存储初始化失败
            </h2>
            <p className='text-gray-600 mb-4'>{initializationError}</p>
            <button
              onClick={() => window.location.reload()}
              className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 如果存储正在初始化，显示加载状态
  if (initializationStatus === 'initializing') {
    return (
      <div className={`flex flex-col h-screen bg-white ${className || ''}`}>
        <Header variant='batch-editor' />
        <div className='flex-1 flex items-center justify-center'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4'></div>
            <p className='text-gray-600'>
              {/* TODO: 需要国际化 */}正在初始化存储...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-screen bg-white ${className || ''}`}>
      {/* 始终渲染 dropzone 的 input 元素，确保 open() 函数可用 */}
      <input {...getInputProps()} />
      {/* 顶部导航栏 */}
      <Header variant='batch-editor' />

      {/* 主要内容区域 */}
      <div className='flex flex-1'>
        {/* 左侧导航 */}
        <SideNavigation
          activeItem={activeFeature}
          onItemChange={handleFeatureChange}
        />

        {/* 中间设置面板 */}
        {renderSettingsPanel()}

        {/* 右侧图片区域 */}
        <ImageUploadGrid
          getRootProps={getRootProps}
          open={open}
          isDragActive={isDragActive}
          handleLoadFromUrl={handleLoadFromUrl}
          imagesCount={images.length}
          images={images}
          processingImageIds={processingImageIds}
        />
      </div>
    </div>
  );
}
