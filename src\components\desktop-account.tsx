'use client';

import { Header } from '@/components/common/Header';
import Image from 'next/image';
import { useEffect, useState, useCallback } from 'react';
import { accountInfoStore } from '@/store/accountStore';

import { addDays, format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { Button } from '@/components/ui/Button';
import { Calendar } from '@/components/ui/Calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/Popover';

import TimezoneSelect from 'react-timezone-select';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';

import { getHistoryData, HistoryData } from '@/api/auth';

import DataChart from '@/components/common/DataChart';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/TableComp';

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/Pagination';

import { dateRangeHandler } from '@/lib/dateUtils';
import { useTranslations } from 'next-intl';

export function DesktopAccount() {
  const account = useTranslations('account');
  const common = useTranslations('common');

  // 用户信息相关的状态
  const userInfo = accountInfoStore(s => s.userInfo);

  const userAavatar = userInfo.head_pic || '/apps/icons/avatar.png';

  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(2022, 0, 20),
    to: addDays(new Date(2022, 0, 20), 20),
  });

  // 处理日期选择
  const handleDateSelect = (range: DateRange | undefined) => {
    const updatedRange = dateRangeHandler(range, date);
    setDate(updatedRange);
  };

  const [selectedTz, setSelectedTz] = useState(
    Intl.DateTimeFormat().resolvedOptions().timeZone
  );

  const [historyData, setHistoryData] = useState<HistoryData[]>([]);

  const getHistoryDataFunc = useCallback(
    async (page: number, reset: boolean) => {
      if (!date?.from || !date?.to) return;
      const res = await getHistoryData({
        page: page,
        page_size: 10,
        start_time: toZonedTime(date.from, selectedTz).getTime(),
        end_time: toZonedTime(date.to, selectedTz).getTime(),
        // start_time: 0,
        // end_time: 0,
      });
      const totalPage = res.pager.total;
      setTotalPage(totalPage);
      const newHistoryData = res.list.map(item => {
        const itemTime = format(item.create_time, 'yyyy-MM-dd hh:mm');
        const newItem = {
          ...item,
          date: itemTime.slice(0, 11),
          time: itemTime.slice(10, 16),
        };
        return newItem;
      });

      // 重新请求用当次的数据，不是则用组合数据
      setHistoryData(prevData =>
        reset
          ? (newHistoryData as HistoryData[])
          : ([...prevData, ...newHistoryData] as HistoryData[])
      );
    },
    [date, selectedTz]
  );

  useEffect(() => {
    setCurPage(1);
    setHistoryData([]);
    getHistoryDataFunc(1, true);
  }, [date, selectedTz, getHistoryDataFunc]);

  // 分页加载更多
  const [curPage, setCurPage] = useState(1);
  const [totalPage, setTotalPage] = useState(1);

  return (
    <div className='min-h-full flex flex-col bg-gray-50 overflow-auto relative'>
      {/* 头部 */}
      <Header variant='desktop' />
      {/* 内容 */}
      <div className='p-6 mx-auto w-[1200px]'>
        <div className='bg-white rounded-2xl shadow-lg p-6'>
          {/* 个人信息   */}
          <div className='flex items-center justify-between border-b-1 pb-4'>
            <div className='flex items-center'>
              <figure className='w-12 h-12 flex-shrink-0'>
                <Image
                  src={userAavatar}
                  alt='avatar'
                  width={48}
                  height={48}
                  className='w-12 h-12 rounded-full object-cover'
                  unoptimized={true}
                />
              </figure>
              <span className='flex-1 text-base text-black ms-4 truncate'>
                {userInfo.email}
              </span>
            </div>
            {/* 跳转个人中心 */}
            <div className='text-sm'>
              <a
                href={process.env.NEXT_PUBLIC_ACCOUNT_URL}
                className='underline hover:text-[#FFCC03]'
              >
                {account('goToAccountCenter')}
              </a>
            </div>
          </div>
          {/* 日期范围选择器和时区选择器 */}
          <div className='flex gap-4 mt-4 mb-1'>
            {/* 日期范围选择器 */}
            <div className='w-[312px]'>
              <p className='text-gray-500 mb-1 text-[14px]'>
                {account('timeFrame')}
              </p>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id='date'
                    variant={'outline'}
                    className={`w-full justify-between text-left font-normal focus:border-[#FFCC03] focus:ring-[#FFCC03]
                    ${!date && 'text-muted-foreground'}`}
                  >
                    {date?.from ? (
                      date.to ? (
                        <>
                          {format(date.from, 'yyyy-MM-dd')} -{' '}
                          {format(date.to, 'yyyy-MM-dd')}
                        </>
                      ) : (
                        format(date.from, 'yyyy-MM-dd')
                      )
                    ) : (
                      <span>{account('pickDate')}</span>
                    )}
                    <CalendarIcon />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className='w-full p-0' align='start'>
                  <Calendar
                    initialFocus
                    mode='range'
                    defaultMonth={date?.from}
                    selected={date}
                    onSelect={handleDateSelect}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>
            {/* 时区选择器 */}
            <div className='w-[312px]'>
              <p className=' text-gray-500 mb-1 text-[14px]'>
                {account('timeZone')}
              </p>
              <TimezoneSelect
                value={selectedTz}
                onChange={tz => setSelectedTz(tz.value)}
                classNamePrefix='tz-select'
                styles={{
                  option: (provided, state) => ({
                    ...provided,
                    '&:focus-within': {
                      borderColor: '#FFCC03',
                      boxShadow: '0 0 0 1px #FFCC03',
                    },
                    backgroundColor: state.isSelected
                      ? 'rgba(255, 204, 3, 0.3)' // 选中项背景色
                      : state.isFocused
                        ? '#f0f0f0' // 悬停颜色
                        : provided.backgroundColor,
                    color: state.isSelected ? 'black' : provided.color,
                    '&:active': {
                      backgroundColor: '#e0e0e0',
                    },
                  }),
                  control: base => ({
                    ...base,
                    '&:focus-within': {
                      borderColor: '#FFCC03',
                      boxShadow: '0 0 0 1px #FFCC03',
                    },
                  }),
                }}
              />
            </div>
          </div>
          {/* 历史数据 */}
          <div className='mt-8'>
            <Tabs defaultValue='history' className=''>
              <TabsList className='grid grid-cols-2 h-12 font-medium bg-transparent'>
                <TabsTrigger
                  value='history'
                  className='data-[state=active]:border-b-2 data-[state=active]:border-b-[rgba(255,204,3,1)] rounded-none shadow-none'
                >
                  {account('transactionHistory')}
                </TabsTrigger>
                <TabsTrigger
                  value='chart'
                  className='data-[state=active]:border-b-2 data-[state=active]:border-b-[rgba(255,204,3,1)] rounded-none shadow-none'
                >
                  {account('usageChart')}
                </TabsTrigger>
              </TabsList>
              <TabsContent value='history'>
                {/* 历史数据列表 */}
                <Table className='rounded-2xl overflow-hidden'>
                  <TableHeader className='h-14 bg-[#F0F0F0]'>
                    <TableRow className='border-b border-gray-200'>
                      <TableHead className='w-1/4 text-center border-r border-gray-200 font-bold text-gray-800'>
                        {account('date')}
                      </TableHead>
                      <TableHead className='w-1/4 text-center border-r border-gray-200 font-bold text-gray-800'>
                        {account('time')}
                      </TableHead>
                      <TableHead className='w-1/4 text-center border-r border-gray-200 font-bold text-gray-800'>
                        {account('amount')}
                      </TableHead>
                      <TableHead className='w-1/4 text-center font-bold text-gray-800'>
                        {account('description')}
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {historyData.length ? (
                      historyData.map((item, index) => (
                        <TableRow
                          key={index}
                          className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} border-b-0 h-12`}
                        >
                          <TableCell className='text-center border-r border-gray-200'>
                            {item.date}
                          </TableCell>
                          <TableCell className='text-center border-r border-gray-200'>
                            {item.time}
                          </TableCell>
                          <TableCell className='text-center border-r border-gray-200'>
                            {item.num}
                          </TableCell>
                          <TableCell className='text-center'>
                            {item.remark}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow className='w-full'>
                        <TableCell colSpan={4} className='h-[300px]'>
                          <div className='flex-col items-center justify-center'>
                            <Image
                              src='/apps/null_img.png'
                              alt='null_img'
                              width={150}
                              height={120}
                              className='mx-auto my-4'
                            ></Image>
                            <div className='text-center py-4'>
                              {common('noMoreData')}
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
                {/* 分页 */}
                {totalPage > 5 && (
                  <Pagination className='mt-2'>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          href='#'
                          onClick={e => {
                            e.preventDefault();
                            if (curPage > 1) {
                              setCurPage(prev => prev - 1);
                              getHistoryDataFunc(curPage - 1, true);
                            }
                          }}
                          className={
                            curPage <= 1 ? 'pointer-events-none opacity-50' : ''
                          }
                        />
                      </PaginationItem>
                      {Array.from({ length: Math.min(5, totalPage) }).map(
                        (_, i) => {
                          const pageNum = curPage - 2 + i;
                          if (pageNum > 0 && pageNum <= totalPage) {
                            return (
                              <PaginationItem key={pageNum}>
                                <PaginationLink
                                  href='#'
                                  onClick={e => {
                                    e.preventDefault();
                                    setCurPage(pageNum);
                                    getHistoryDataFunc(pageNum, true);
                                  }}
                                  isActive={pageNum === curPage}
                                  className={
                                    pageNum === curPage ? 'bg-[#FFCC03]' : ''
                                  }
                                >
                                  {pageNum}
                                </PaginationLink>
                              </PaginationItem>
                            );
                          }
                          return null;
                        }
                      )}
                      {totalPage > 5 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}
                      <PaginationItem>
                        <PaginationNext
                          href='#'
                          onClick={e => {
                            e.preventDefault();
                            if (curPage < totalPage) {
                              setCurPage(prev => prev + 1);
                              getHistoryDataFunc(curPage + 1, true);
                            }
                          }}
                          className={
                            curPage >= totalPage
                              ? 'pointer-events-none opacity-50'
                              : ''
                          }
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                )}
              </TabsContent>
              <TabsContent value='chart'>
                {/* 图表 */}
                <div className='mt-4'>
                  <DataChart historyData={historyData} />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
