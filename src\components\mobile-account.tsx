'use client';

import { Header } from '@/components/common/Header';
import Image from 'next/image';
import { useEffect, useState, useCallback } from 'react';
import { accountInfoStore } from '@/store/accountStore';

import { addDays, format } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { Button } from '@/components/ui/Button';
import { Calendar } from '@/components/ui/Calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/Popover';

import TimezoneSelect from 'react-timezone-select';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';

import { getHistoryData, HistoryData } from '@/api/auth';

import DataChart from '@/components/common/DataChart';

import { InfiniteScrollList } from '@/components/common/InfiniteScroll';

import { dateRangeHandler } from '@/lib/dateUtils';
import { useTranslations } from 'next-intl';

export function MobileAccount() {
  const account = useTranslations('account');
  const common = useTranslations('common');

  // 用户信息相关的状态
  const userInfo = accountInfoStore(s => s.userInfo);

  const userAavatar = userInfo.head_pic || '/apps/icons/avatar.png';

  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(2022, 0, 20),
    to: addDays(new Date(2022, 0, 20), 20),
  });

  // 处理日期选择
  const handleDateSelect = (range: DateRange | undefined) => {
    const updatedRange = dateRangeHandler(range, date);
    setDate(updatedRange);
  };

  const [selectedTz, setSelectedTz] = useState(
    Intl.DateTimeFormat().resolvedOptions().timeZone
  );

  const [historyData, setHistoryData] = useState<HistoryData[]>([]);

  const getHistoryDataFunc = useCallback(
    async (page: number, reset: boolean) => {
      if (!date?.from || !date?.to) return;
      const res = await getHistoryData({
        page: page,
        page_size: 10,
        start_time: toZonedTime(date.from, selectedTz).getTime(),
        end_time: toZonedTime(date.to, selectedTz).getTime(),
        // start_time: 0,
        // end_time: 0,
      });
      setIsLoading(false);
      setHasMore(res.list.length > 0);
      const newHistoryData = res.list.map(item => {
        const itemTime = format(item.create_time, 'yyyy-MM-dd hh:mm');
        const newItem = {
          ...item,
          date: itemTime.slice(0, 11),
          time: itemTime.slice(10, 16),
        };
        return newItem;
      });

      // 重新请求用当次的数据，不是则用组合数据
      setHistoryData(prevData =>
        reset
          ? (newHistoryData as HistoryData[])
          : ([...prevData, ...newHistoryData] as HistoryData[])
      );
    },
    [date, selectedTz]
  );

  useEffect(() => {
    setCurPage(1);
    setHistoryData([]);
    setHasMore(true);
    getHistoryDataFunc(1, true);
  }, [date, selectedTz, getHistoryDataFunc]);

  // 分页加载更多
  const [curPage, setCurPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const loadMore = useCallback(async () => {
    if (isLoading || !hasMore) return;
    setIsLoading(true);
    const nextPage = curPage + 1;
    setCurPage(nextPage);
    await getHistoryDataFunc(curPage, false);
  }, [curPage, hasMore, isLoading, getHistoryDataFunc]);

  return (
    <div className='min-h-full flex flex-col bg-gray-50 overflow-auto relative'>
      {/* 头部 */}
      <Header variant='mobile' />
      {/* 内容 */}
      <div className='p-6'>
        <div className='bg-white rounded-2xl shadow-lg p-6'>
          {/* 个人信息   */}
          <div className='flex items-center'>
            <figure className='w-12 h-12 flex-shrink-0'>
              <Image
                src={userAavatar}
                alt='avatar'
                width={48}
                height={48}
                className='w-12 h-12 rounded-full object-cover'
                unoptimized={true}
              />
            </figure>
            <span className='flex-1 text-base text-black ms-4 truncate'>
              {userInfo.email}
            </span>
          </div>
          {/* 跳转个人中心 */}
          <div className='mt-2 text-sm pb-4 border-b-1'>
            <a href={process.env.NEXT_PUBLIC_ACCOUNT_URL} className='underline'>
              {account('goToAccountCenter')}
            </a>
          </div>
          {/* 日期范围选择器 */}
          <div>
            <p className='mt-4 mb-1 text-gray-500'>{account('timeFrame')}</p>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id='date'
                  variant={'outline'}
                  className={`w-full justify-between text-left font-normal focus:border-[#FFCC03] focus:ring-[#FFCC03]  
                    ${!date && 'text-muted-foreground'}`}
                >
                  {date?.from ? (
                    date.to ? (
                      <>
                        {format(date.from, 'yyyy-MM-dd')} -{' '}
                        {format(date.to, 'yyyy-MM-dd')}
                      </>
                    ) : (
                      format(date.from, 'yyyy-MM-dd')
                    )
                  ) : (
                    <span>{account('pickDate')}</span>
                  )}
                  <CalendarIcon />
                </Button>
              </PopoverTrigger>
              <PopoverContent className='w-full p-0' align='start'>
                <Calendar
                  initialFocus
                  mode='range'
                  defaultMonth={date?.from}
                  selected={date}
                  onSelect={handleDateSelect}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>
          {/* 时区选择器 */}
          <div>
            <p className='mt-4 mb-1 text-gray-500'>{account('timeZone')}</p>
            <TimezoneSelect
              value={selectedTz}
              onChange={tz => setSelectedTz(tz.value)}
              classNamePrefix='tz-select'
              styles={{
                option: (provided, state) => ({
                  ...provided,
                  '&:focus-within': {
                    borderColor: '#FFCC03',
                    boxShadow: '0 0 0 1px #FFCC03',
                  },
                  backgroundColor: state.isSelected
                    ? 'rgba(255, 204, 3, 0.3)' // 选中项背景色
                    : state.isFocused
                      ? '#f0f0f0' // 悬停颜色
                      : provided.backgroundColor,
                  color: state.isSelected ? 'black' : provided.color,
                  '&:active': {
                    backgroundColor: '#e0e0e0',
                  },
                }),
                control: base => ({
                  ...base,
                  '&:focus-within': {
                    borderColor: '#FFCC03',
                    boxShadow: '0 0 0 1px #FFCC03',
                  },
                }),
              }}
            />
          </div>
          {/* 历史数据 */}
          <div className='mt-8'>
            <Tabs
              defaultValue='history'
              className='w-full [&_[data-state=inactive]]:bg-[#E7E7E7] [&_[data-state=inactive]]:text-[#878787]'
            >
              <TabsList className='grid w-full grid-cols-2 h-12 font-medium'>
                <TabsTrigger value='history'>
                  {account('transactionHistory')}
                </TabsTrigger>
                <TabsTrigger value='chart'>{account('usageChart')}</TabsTrigger>
              </TabsList>
              <TabsContent value='history'>
                {/* 历史数据列表 */}
                <InfiniteScrollList
                  onLoadMore={loadMore}
                  isLoading={isLoading}
                  hasMore={hasMore}
                >
                  {isLoading || historyData.length ? (
                    <ul>
                      {historyData.map((item, index) => (
                        <li
                          key={index}
                          className='flex-col text-[#121212] text-sm font-medium border-b-1 py-2'
                        >
                          <div className='flex justify-between my-2'>
                            <span>{account('date')}:</span>
                            <span>{item.date}</span>
                          </div>
                          <div className='flex justify-between my-2'>
                            <span>{account('time')}:</span>
                            <span>{item.time}</span>
                          </div>
                          <div className='flex justify-between my-2'>
                            <span>{account('amount')}:</span>
                            <span>{item.num}</span>
                          </div>
                          <div className='flex justify-between my-2'>
                            <span>{account('description')}:</span>
                            <span>{item.remark}</span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <div className='h-[240px] flex-col items-center justify-center'>
                      <Image
                        src='/apps/null_img.png'
                        alt='null_img'
                        width={150}
                        height={120}
                        className='mx-auto my-4'
                      ></Image>
                      <div className='text-center py-4'>
                        {common('noMoreData')}
                      </div>
                    </div>
                  )}
                </InfiniteScrollList>
              </TabsContent>
              <TabsContent value='chart'>
                {/* 图表 */}
                <div className='mt-4'>
                  <DataChart historyData={historyData} />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
