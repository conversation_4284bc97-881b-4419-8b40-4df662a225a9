{"common": {"cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "upload": "Upload", "download": "Download", "apply": "Apply", "processing": "Processing", "free": "Free", "unlock": "Unlock", "previousStep": "Previous step", "nextStep": "Next step", "compareOriginal": "Compare original image", "deleteAll": "Delete all", "keep": "Keep", "undo": "Undo", "redo": "Redo", "reset": "Reset", "or": "or", "url": "URL", "uploadImage": "Upload Image", "selectImage": "Select", "previous": "Previous", "next": "Next", "morePages": "More pages", "loading": "Loading...", "noMoreData": "No more data", "noDownloadableImages": "No downloadable images", "downloadFailed": "Download failed, please try again"}, "auth": {"signIn": "Sign in", "signUp": "Sign up", "myAccount": "My account", "logout": "Logout"}, "singleImage": {"initial": {"removeBackground": "Remove BG", "uploadImageToRemove": "Upload Image to Remove Background", "dragAndDrop": "Drag and drop your image here", "uploadImage": "Upload Image", "pasteImage": "Paste image {shortcut} or <url>URL</url>", "pasteImageMobile": "or <url>URL</url>", "supportedFormats": "Supported formats:", "noImageTryThese": "No image? Try one of these:", "recaptchaNotice": "This site is protected by reCAPTCHA and the Google Privacy Policy and Terms of service apply.", "pasteImageUrl": "Paste Image URL", "pleaseInputImageUrl": "Please input image URL"}, "interface": {"changeBackgroundColors": "Change Background Colors", "changeBackgroundPhotos": "Change Background Photos", "eraseRestore": "Erase / Restore", "blurBackground": "Blur Background", "previewSize": "Preview", "maxQualitySize": "Max Quality"}, "backgroundColors": {"customColor": "Custom Color", "presetColor": "Preset Color"}, "backgroundPhotos": {"gradient": "Gradient", "landscape": "Landscape", "geometric": "Geometric", "wood": "<PERSON>", "paper": "Paper", "texture": "Texture"}, "eraseRestore": {"brushSize": "Brush Size", "zoomOut": "Zoom out", "zoomIn": "Zoom in", "grip": "<PERSON><PERSON>", "handTool": "Hand tool - click to drag", "title": "Erase / Restore", "erase": "Erase", "restore": "Rest<PERSON>", "deactivateHandTool": "Deactivate hand tool"}, "backgroundBlur": {"enableBackgroundBlur": "Enable Background Blur", "blurAmount": "Blur amount"}, "shadow": {"opacity": "Opacity"}}, "batchEditor": {"interface": {"batchEditor": "Batch Editor", "background": "Background", "resize": "Resize", "rename": "<PERSON><PERSON>", "convert": "Convert", "compress": "Compress"}, "background": {"color": "Color", "photos": "Photos"}, "resize": {"marketplacePlatforms": "For Marketplace Platforms", "tiktokShop": "TikTok shop", "amazon": "Amazon", "ebay": "eBay", "postmark": "Postmark", "depop": "Depop", "mercari": "<PERSON><PERSON><PERSON>", "mercadoLibre": "Mercado Libre", "shopee": "<PERSON>ee", "shopifySquare": "Shopify square", "shopifyLandscape": "Shopify landscape", "shopifyPortrait": "Shopify portrait", "lazada": "<PERSON><PERSON><PERSON>", "etsy": "Etsy", "vinted": "Vinted", "socialMediaPlatforms": "For Social Media Platforms", "instagramStory": "Instagram story", "instagramPost": "Instagram post", "facebookCover": "Facebook cover", "facebookPost": "Facebook post", "facebookMarketplace": "Facebook Marketplace", "tiktokPost": "TikTok post", "tiktokCover": "TikTok cover", "youtubeCover": "YouTube cover", "youtubeChannel": "YouTube channel", "twitterCover": "Twitter cover", "twitterPost": "Twitter post", "ratioSize": "<PERSON><PERSON>", "square": "Square (1:1)", "passportId": "Passport & lD Photo (2x2 in)", "customSize": "Custom Size", "byScale": "By Scale", "byDimensions": "By Dimensions", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "lockAspectRatio": "Lock Aspect Ratio", "unlockAspectRatio": "Unlock Aspect Ratio", "pixelPx": "Pixel (px)", "inchIn": "Inch (in)", "millimeterMm": "Millimeter (mm)", "originalSize": "Original Size", "selectCategory": "Select a category", "ratio": "<PERSON><PERSON>", "custom": "Custom"}, "rename": {"prefix": "Prefix", "pleaseEnterName": "Please enter a name", "startNumber": "Start Number", "numberStep": "Number Step"}, "compress": {"compressionLevel": "Compression Level", "customSize": "Custom Size", "original": "Original", "lightBestClarity": "Light (Best Clarity)", "mediumBalanced": "Medium (Balanced Quality)", "highSmallest": "High (Smallest Size)", "eachImageUnder": "Each Image Under", "enterNumberOnly": "Enter a number only", "supportedRange": "Supported range"}}, "messages": {"singleImage": {"unableToOpenImage": "Unable to open image.The URL is invalid or wrong.", "imagesExceedLimit": "The number of images exceeds the limit, and a maximum of {count} images can be uploaded.", "confirmDelete": "Are you sure you want to delete?", "sorryCouldntRemove": "Sorry,we couldn't remove the background. Try brushing over it with Brush.", "tryBrush": "Try Brush", "imageUploadFailed": "Image upload failed.", "unsupportedImageFormat": "Unsupported image format.", "tryBrushSuggestion": "Try brushing over it with Brush."}, "batchEditor": {"confirmDeleteAll": "Are you sure you want to delete all images?", "sorryCouldntRemoveBatch": "Sorry,we couldn't remove the background.", "allProcessedSuccessfully": "All processed successfully", "someImagesFailed": "{failed} of {total} images failed to process", "batchFailed": "<PERSON><PERSON> failed", "sizeTooSmall": "Size too small. Minimum value is 100 px (≈ 1 inch or 25 mm).", "sizeTooLarge": "Size too large. Maximum value is 4000 px (≈ 42 inches or 1066 mm).", "pleaseEnterValidFileSize": "Please enter a valid file size.", "fileSizeMinimum": "File size must be at least 1 KB.", "fileSizeMaximum": "File size cannot exceed 10 MB."}}, "account": {"credits": "Credits", "myAccount": "My account", "goToTenorshareAccount": "Go to Tenorshare Accout Center", "timeFrame": "Time frame", "past30Days": "in the past 30 days", "past14Days": "in the past 14 days", "past7Days": "in the past 7 days", "timeZone": "Time zone", "transactionHistory": "Transaction History", "usageChart": "Usage Chart", "date": "Date", "time": "Time", "amount": "Amount", "description": "Description", "bonusCredits": "Bonus Credits", "subscriptionCredits": "Subscription Credits", "bonusExpired": "Bonus expired", "subscriptionExpired": "Subscription expired", "removeBG": "RemoveBG", "batchEditorRemoveBG": "Batch Editor RemoveBG", "receivedFreeCredits": "You've received <count>5</count> free AI credits today.", "usedAllFreeCredits": "You've used all 5 free credits for today. Upgrade for more credits →", "advancedAI": "Advanced AI", "advancedAIUsage": "Advanced AI | <count>3</count><separator>/</separator><total>5</total> free credits used", "advancedAIEnabled": "Advanced AI enabled · View usage →", "monthlyLimitReached": "You've reached your monthly usage limit. Upgrade to continue →", "pickDate": "Pick a date"}, "mobile": {"selectPhotos": "Please select 1–10 photos", "upload": "Upload", "select": "Select", "selected": "Selected", "allowAccessPhotos": "Allow Access to Your Photos", "accessPhotosDescription": "To upload and edit your photos, we need access to your photo library. We'll only access the photos you select.", "notNow": "Not Now", "allowAccess": "Allow Access", "accessDenied": "Access Denied", "accessDeniedDescription": "You've denied access to your photo library. To upload images, please allow access in your device settings.", "goToSettings": "Go to Settings", "bgColors": "BG  Colors", "bgPhotos": "BG Photos", "eraseRestore": "Erase / Restore", "blurBG": "Blur BG"}, "error": {"title": "Something went wrong", "message": "The application encountered an unexpected error. Please try refreshing the page.", "viewDetails": "View error details", "refresh": "Refresh page"}, "metadata": {"title": "AI Background Removal <PERSON>l - Smart Image Background Processing", "description": "Use advanced AI technology to remove image backgrounds with one click. Support background replacement, color adjustment, shadow addition and other professional editing functions."}}